/**
 * Standardized Environment Variable Configuration
 * 
 * This file provides a consistent way to access environment variables
 * across all Luminar applications with proper typing and validation.
 */

// Environment types
export type Environment = 'development' | 'staging' | 'production' | 'test';

// Base environment configuration interface
export interface BaseEnvConfig {
  // Environment
  NODE_ENV: Environment;
  APP_NAME: string;
  APP_VERSION: string;
  
  // API Configuration
  API_BASE_URL: string;
  WS_URL?: string;
  
  // Database
  DATABASE_URL: string;
  DATABASE_HOST?: string;
  DATABASE_PORT?: number;
  DATABASE_NAME?: string;
  DATABASE_USERNAME?: string;
  DATABASE_PASSWORD?: string;
  
  // Redis
  REDIS_URL?: string;
  REDIS_HOST?: string;
  REDIS_PORT?: number;
  REDIS_PASSWORD?: string;
  
  // Authentication
  JWT_SECRET: string;
  JWT_EXPIRES_IN?: string;
  SESSION_SECRET?: string;
  
  // External Services
  OPENAI_API_KEY?: string;
  SENTRY_DSN?: string;
  ANALYTICS_ID?: string;
  
  // Feature Flags
  ENABLE_ANALYTICS?: boolean;
  ENABLE_SENTRY?: boolean;
  ENABLE_WEBSOCKET?: boolean;
  ENABLE_OFFLINE?: boolean;
  
  // Development
  DEBUG?: boolean;
  LOG_LEVEL?: string;
}

// Frontend-specific environment configuration
export interface FrontendEnvConfig extends Partial<BaseEnvConfig> {
  // Vite-specific variables (prefixed with VITE_)
  VITE_API_BASE_URL: string;
  VITE_WS_URL?: string;
  VITE_APP_NAME: string;
  VITE_APP_VERSION: string;
  VITE_ENABLE_ANALYTICS?: string;
  VITE_ENABLE_SENTRY?: string;
  VITE_ENABLE_WEBSOCKET?: string;
  VITE_ENABLE_OFFLINE?: string;
  VITE_SENTRY_DSN?: string;
  VITE_ANALYTICS_ID?: string;
}

// Backend-specific environment configuration
export interface BackendEnvConfig extends BaseEnvConfig {
  PORT: number;
  
  // Additional backend-specific variables
  CORS_ORIGINS?: string;
  RATE_LIMIT_WINDOW?: number;
  RATE_LIMIT_MAX?: number;
  
  // File Upload
  UPLOAD_DIR?: string;
  MAX_FILE_SIZE?: number;
  
  // Vector Database
  QDRANT_HOST?: string;
  QDRANT_PORT?: number;
  QDRANT_API_KEY?: string;
  
  // Object Storage
  MINIO_ENDPOINT?: string;
  MINIO_ACCESS_KEY?: string;
  MINIO_SECRET_KEY?: string;
  MINIO_BUCKET?: string;
}

/**
 * Get environment variable with type safety and validation
 */
export function getEnvVar<T = string>(
  key: string,
  defaultValue?: T,
  transform?: (value: string) => T
): T {
  const value = process.env[key] || (typeof window !== 'undefined' && (window as any).env?.[key]);
  
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is required but not set`);
  }
  
  if (transform) {
    return transform(value);
  }
  
  return value as T;
}

/**
 * Parse boolean environment variable
 */
export function getBooleanEnv(key: string, defaultValue = false): boolean {
  return getEnvVar(key, defaultValue.toString(), (value) => 
    value.toLowerCase() === 'true' || value === '1'
  );
}

/**
 * Parse number environment variable
 */
export function getNumberEnv(key: string, defaultValue?: number): number {
  return getEnvVar(key, defaultValue?.toString(), (value) => {
    const num = parseInt(value, 10);
    if (isNaN(num)) {
      throw new Error(`Environment variable ${key} must be a valid number, got: ${value}`);
    }
    return num;
  });
}

/**
 * Parse array environment variable (comma-separated)
 */
export function getArrayEnv(key: string, defaultValue: string[] = []): string[] {
  return getEnvVar(key, defaultValue.join(','), (value) => 
    value.split(',').map(item => item.trim()).filter(Boolean)
  );
}

/**
 * Validate required environment variables
 */
export function validateRequiredEnvVars(requiredVars: string[]): void {
  const missing = requiredVars.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}\n` +
      'Please check your .env file or environment configuration.'
    );
  }
}

/**
 * Get standardized environment configuration for frontend apps
 */
export function getFrontendEnvConfig(): FrontendEnvConfig {
  return {
    VITE_API_BASE_URL: getEnvVar('VITE_API_BASE_URL', 'http://localhost:3000/api'),
    VITE_WS_URL: getEnvVar('VITE_WS_URL', 'ws://localhost:3000/ws'),
    VITE_APP_NAME: getEnvVar('VITE_APP_NAME', 'Luminar App'),
    VITE_APP_VERSION: getEnvVar('VITE_APP_VERSION', '1.0.0'),
    VITE_ENABLE_ANALYTICS: getEnvVar('VITE_ENABLE_ANALYTICS', 'false'),
    VITE_ENABLE_SENTRY: getEnvVar('VITE_ENABLE_SENTRY', 'false'),
    VITE_ENABLE_WEBSOCKET: getEnvVar('VITE_ENABLE_WEBSOCKET', 'true'),
    VITE_ENABLE_OFFLINE: getEnvVar('VITE_ENABLE_OFFLINE', 'false'),
    VITE_SENTRY_DSN: getEnvVar('VITE_SENTRY_DSN'),
    VITE_ANALYTICS_ID: getEnvVar('VITE_ANALYTICS_ID'),
  };
}

/**
 * Get standardized environment configuration for backend apps
 */
export function getBackendEnvConfig(): BackendEnvConfig {
  return {
    NODE_ENV: getEnvVar('NODE_ENV', 'development') as Environment,
    APP_NAME: getEnvVar('APP_NAME', 'Luminar Backend'),
    APP_VERSION: getEnvVar('APP_VERSION', '1.0.0'),
    PORT: getNumberEnv('PORT', 3000),
    
    // API
    API_BASE_URL: getEnvVar('API_BASE_URL', 'http://localhost:3000/api'),
    WS_URL: getEnvVar('WS_URL', 'ws://localhost:3000/ws'),
    
    // Database
    DATABASE_URL: getEnvVar('DATABASE_URL'),
    DATABASE_HOST: getEnvVar('DATABASE_HOST', 'localhost'),
    DATABASE_PORT: getNumberEnv('DATABASE_PORT', 5432),
    DATABASE_NAME: getEnvVar('DATABASE_NAME', 'luminar'),
    DATABASE_USERNAME: getEnvVar('DATABASE_USERNAME', 'postgres'),
    DATABASE_PASSWORD: getEnvVar('DATABASE_PASSWORD'),
    
    // Redis
    REDIS_URL: getEnvVar('REDIS_URL'),
    REDIS_HOST: getEnvVar('REDIS_HOST', 'localhost'),
    REDIS_PORT: getNumberEnv('REDIS_PORT', 6379),
    REDIS_PASSWORD: getEnvVar('REDIS_PASSWORD'),
    
    // Authentication
    JWT_SECRET: getEnvVar('JWT_SECRET'),
    JWT_EXPIRES_IN: getEnvVar('JWT_EXPIRES_IN', '15m'),
    SESSION_SECRET: getEnvVar('SESSION_SECRET'),
    
    // External Services
    OPENAI_API_KEY: getEnvVar('OPENAI_API_KEY'),
    SENTRY_DSN: getEnvVar('SENTRY_DSN'),
    ANALYTICS_ID: getEnvVar('ANALYTICS_ID'),
    
    // Feature Flags
    ENABLE_ANALYTICS: getBooleanEnv('ENABLE_ANALYTICS', false),
    ENABLE_SENTRY: getBooleanEnv('ENABLE_SENTRY', false),
    ENABLE_WEBSOCKET: getBooleanEnv('ENABLE_WEBSOCKET', true),
    ENABLE_OFFLINE: getBooleanEnv('ENABLE_OFFLINE', false),
    
    // Development
    DEBUG: getBooleanEnv('DEBUG', false),
    LOG_LEVEL: getEnvVar('LOG_LEVEL', 'info'),
    
    // Backend-specific
    CORS_ORIGINS: getEnvVar('CORS_ORIGINS', 'http://localhost:3001,http://localhost:3002'),
    RATE_LIMIT_WINDOW: getNumberEnv('RATE_LIMIT_WINDOW', 900000), // 15 minutes
    RATE_LIMIT_MAX: getNumberEnv('RATE_LIMIT_MAX', 100),
    
    // File Upload
    UPLOAD_DIR: getEnvVar('UPLOAD_DIR', './uploads'),
    MAX_FILE_SIZE: getNumberEnv('MAX_FILE_SIZE', 10485760), // 10MB
    
    // Vector Database
    QDRANT_HOST: getEnvVar('QDRANT_HOST', 'localhost'),
    QDRANT_PORT: getNumberEnv('QDRANT_PORT', 6333),
    QDRANT_API_KEY: getEnvVar('QDRANT_API_KEY'),
    
    // Object Storage
    MINIO_ENDPOINT: getEnvVar('MINIO_ENDPOINT', 'localhost'),
    MINIO_ACCESS_KEY: getEnvVar('MINIO_ACCESS_KEY', 'minioadmin'),
    MINIO_SECRET_KEY: getEnvVar('MINIO_SECRET_KEY', 'minioadmin'),
    MINIO_BUCKET: getEnvVar('MINIO_BUCKET', 'luminar'),
  };
}

// Export commonly used environment configurations
export const isDevelopment = getEnvVar('NODE_ENV', 'development') === 'development';
export const isProduction = getEnvVar('NODE_ENV', 'development') === 'production';
export const isTest = getEnvVar('NODE_ENV', 'development') === 'test';
