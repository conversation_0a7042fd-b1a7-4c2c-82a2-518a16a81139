# ===================================================================
# Luminar Platform Environment Configuration
# ===================================================================
# Copy this file to .env and update the values for your environment
#
# IMPORTANT: Never commit .env files to version control!
# ===================================================================

# ===================================================================
# Environment & Application
# ===================================================================
NODE_ENV=development
APP_NAME=Luminar Platform
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=info

# ===================================================================
# API Configuration
# ===================================================================
# Backend API
API_BASE_URL=http://localhost:3000/api
PORT=3000

# WebSocket
WS_URL=ws://localhost:3000/ws
ENABLE_WEBSOCKET=true

# CORS Configuration
CORS_ORIGINS=http://localhost:3001,http://localhost:3002,http://localhost:3003,http://localhost:3004,http://localhost:3005,http://localhost:3006,http://localhost:3007

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# ===================================================================
# Database Configuration
# ===================================================================
# PostgreSQL
DATABASE_URL=postgresql://postgres:password@localhost:5432/luminar
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=luminar
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password

# ===================================================================
# Redis Configuration
# ===================================================================
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# ===================================================================
# Authentication & Security
# ===================================================================
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=30d

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-in-production

# ===================================================================
# External Services
# ===================================================================
# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Sentry (Error Tracking)
SENTRY_DSN=your-sentry-dsn
ENABLE_SENTRY=false

# Analytics
ANALYTICS_ID=your-analytics-id
ENABLE_ANALYTICS=false

# ===================================================================
# Vector Database (Qdrant)
# ===================================================================
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=

# ===================================================================
# Object Storage (MinIO)
# ===================================================================
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=luminar
MINIO_USE_SSL=false

# ===================================================================
# File Upload Configuration
# ===================================================================
UPLOAD_DIR=./uploads
UPLOAD_TEMP_DIR=./temp
MAX_FILE_SIZE=10485760
ALLOWED_MIME_TYPES=image/jpeg,image/png,application/pdf,text/plain

# ===================================================================
# Feature Flags
# ===================================================================
ENABLE_OFFLINE=false
ENABLE_SSO=true
ENABLE_MFA=false
ENABLE_PUSH_NOTIFICATIONS=false

# ===================================================================
# Frontend Environment Variables (Vite)
# ===================================================================
# These variables are exposed to the frontend and must be prefixed with VITE_

# API Configuration
VITE_API_BASE_URL=http://localhost:3000/api
VITE_WS_URL=ws://localhost:3000/ws

# Application Info
VITE_APP_NAME=Luminar Platform
VITE_APP_VERSION=1.0.0
VITE_ENV=development

# Feature Flags
VITE_ENABLE_WEBSOCKET=true
VITE_ENABLE_OFFLINE=false
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_SENTRY=false

# External Services
VITE_SENTRY_DSN=your-sentry-dsn
VITE_ANALYTICS_ID=your-analytics-id
VITE_SUPPORT_EMAIL=<EMAIL>

# ===================================================================
# Docker Compose Overrides
# ===================================================================
# These are used by docker-compose files

# PostgreSQL
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=luminar
POSTGRES_PORT=5432

# Redis
REDIS_PASSWORD=redis-password

# MinIO
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin
MINIO_DEFAULT_BUCKETS=uploads,documents,backups,training-assets,vendor-files,wins-attachments

# Qdrant
QDRANT_SERVICE_HTTP_PORT=6333
QDRANT_SERVICE_GRPC_PORT=6334
QDRANT_LOG_LEVEL=INFO