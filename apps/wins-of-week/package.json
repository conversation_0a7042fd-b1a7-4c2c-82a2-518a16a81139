{"name": "tanstack-start-example-basic", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "start": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-start": "^1.116.1", "@tanstack/router-generator": "^1.124.0", "@tanstack/start": "^1.116.1", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "react-hook-form": "^7.60.0", "recharts": "^3.0.2", "@luminar/shared-ui": "workspace:*", "@luminar/shared-auth": "workspace:*", "immer": "^10.0.3", "vite": "^6.0.0", "xlsx": "^0.18.5", "zod": "^3.25.71"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "happy-dom": "^15.11.6", "msw": "^2.10.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^2.1.8"}}